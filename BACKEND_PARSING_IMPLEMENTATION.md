# Backend Guest List Parsing Implementation

## Overview
Successfully migrated guest list upload functionality from client-side parsing to backend parsing for improved reliability and consistency.

## Changes Made

### 1. Updated `src/lib/services/guest-list.tsx`

**Added new interfaces:**
- `ParsedGuest`: Backend response format for guest data
- `ParseGuestListResponse`: Backend response structure with guests and parse errors
- `ParseGuestListParams`: Parameters for the parsing function

**Added new function:**
- `parseGuestListFile()`: Calls the backend endpoint `/v1/user/events/guestlist/spreadsheet/parse`
  - Accepts eventId and file parameters
  - Uses multipart/form-data content type
  - Returns parsed guest data and any parsing errors

### 2. Updated `src/pages/prelaunch/create-guest-list/upload-guest-list.tsx`

**Key Changes:**
- **Removed client-side parsing**: Eliminated dependency on `papaparse` and `xlsx` libraries
- **Added backend integration**: Now calls `GuestList.parseGuestListFile()` service
- **Added event context**: Uses `useEventStore` to get the selected event ID
- **Enhanced error handling**: 
  - Concise UI error messages (e.g., "0 Records found, 2 errors found")
  - Detailed errors logged to console for debugging
  - Proper error state management

**New Features:**
- **Data transformation**: Converts backend `ParsedGuest` format to UI `Guest` format
- **Improved success feedback**: Shows success toast with record count
- **Better validation**: Checks for selected event before processing
- **Enhanced UI states**: Better loading and error state management

**UI Improvements:**
- Fixed styling issues (removed stray CSS classes)
- Updated button text from "Upload Guest Record" to "Next"
- Improved error/success indicator colors and messages

### 3. Removed Client-Side Dependencies

**Deleted files:**
- `src/lib/utils/fileParser.ts`: No longer needed with backend parsing

## Backend API Integration

**Endpoint:** `POST /v1/user/events/guestlist/spreadsheet/parse`

**Request Format:**
- Content-Type: `multipart/form-data`
- Parameters: `event_id` (query parameter)
- Body: `file` (form data)

**Response Format:**
```json
{
  "guests": [
    {
      "email": "string",
      "first_name": "string", 
      "last_name": "string",
      "phone_number": "string"
    }
  ],
  "parse_errors": ["string"]
}
```

## Data Flow

1. **File Selection**: User selects/drops CSV or Excel file
2. **Validation**: Client validates file type (.csv, .xlsx, .xls)
3. **Event Check**: Ensures an event is selected
4. **Backend Parsing**: Sends file to backend for parsing
5. **Data Transformation**: Converts backend format to UI format
6. **State Updates**: Updates UI with parsed guests and error counts
7. **Parent Notification**: Passes parsed guests to parent component
8. **Success Feedback**: Shows toast notification with results

## Error Handling

**Client-Side Validation:**
- File type validation (CSV, Excel only)
- Event selection validation
- Network error handling

**Backend Error Processing:**
- Parse errors are logged to console for debugging
- Concise error summaries shown in UI
- Detailed error information available for troubleshooting

**UI Error States:**
- "0 Records found, X errors found" for failed parsing
- "X errors found" indicator for partial success
- "No errors found" for successful parsing

## File Format Support

**Supported File Types:**
- `.csv` files
- `.xlsx` files  
- `.xls` files

**Expected Column Structure:**
The backend handles flexible column name matching for:
- First Name / FirstName / first_name
- Last Name / LastName / last_name  
- Email Address / Email / email
- Phone Number / Phone / phone / Mobile

## Benefits of Backend Parsing

1. **Reliability**: Consistent parsing logic across all clients
2. **Performance**: Reduces client-side processing load
3. **Maintainability**: Centralized parsing logic in backend
4. **Security**: File validation and processing on server
5. **Scalability**: Better handling of large files
6. **Error Handling**: More robust error reporting and logging

## Testing Recommendations

1. **File Upload Testing**: Test with various CSV/Excel formats
2. **Error Scenarios**: Test with malformed files, missing columns
3. **Large Files**: Test performance with large guest lists
4. **Network Issues**: Test error handling for network failures
5. **Event Context**: Test behavior when no event is selected
6. **UI States**: Verify loading, success, and error states display correctly
