import { useState } from 'react';
import { CreateGuest } from '../../../components/reuseables/create-guest';
import { CreateIV } from './create-iv';
import { AddGuest } from './add-guest';
import { Preview } from './preview';
import { CreatedSuccess } from './created-success';
import ex1 from '../../../assets/images/ex1.png';
import ex2 from '../../../assets/images/ex2.png';
import ex3 from '../../../assets/images/ex3.png';
import ex4 from '../../../assets/images/ex4.png';
import ex5 from '../../../assets/images/ex5.png';
import ex6 from '../../../assets/images/ex6.png';
import ex7 from '../../../assets/images/ex7.png';
import ex8 from '../../../assets/images/ex8.png';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient();

interface CreateGuestListProps {
  onClose?: () => void;
}

interface Template {
  id?: string;
  name: string;
  preview_url?: string;
  image?: string;
}

interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

const CreateGuestList = ({ onClose }: CreateGuestListProps) => {
  const [activeStep, setActiveStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [showSuccess, setShowSuccess] = useState(false);
  const [selectedTemplateIndex, setSelectedTemplateIndex] = useState<
    number | null
  >(null);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    null
  );
  const [guests, setGuests] = useState<Guest[]>([]);

  const templateData = [
    { name: 'Floral Elegance', image: ex1 },
    { name: 'Modern Romance', image: ex2 },
    { name: 'Rustic Charm', image: ex3 },
    { name: 'Classic Gold', image: ex4 },
    { name: 'Midnight Dream', image: ex5 },
    { name: 'Garden Party', image: ex6 },
    { name: 'Vintage Love', image: ex7 },
    { name: 'Vintage Lve', image: ex8 },
  ];

  const handleStepChange = (step: number) => {
    setActiveStep(step);
  };

  const handleNextStep = () => {
    setCompletedSteps((prev) => [...new Set([...prev, activeStep])]);
    setActiveStep((prevStep) => Math.min(prevStep + 1, 3));
  };

  const handleTemplateSelect = (index: number, template: Template) => {
    setSelectedTemplateIndex(index);
    setSelectedTemplate(template);
    setCompletedSteps((prev) => [...new Set([...prev, activeStep])]);
    handleNextStep();
  };

  const handleSuccess = () => {
    setCompletedSteps([1, 2, 3]);
    setShowSuccess(true);
  };

  const handleGuestsChange = (updatedGuests: Guest[]) => {
    setGuests(updatedGuests);
  };

  const closeModals = () => {
    setActiveStep(1);
    setCompletedSteps([]);
    setShowSuccess(false);
    onClose?.();
  };

  const selectedTemplateImage =
    selectedTemplate?.preview_url ||
    selectedTemplate?.image ||
    (selectedTemplateIndex !== null
      ? templateData[selectedTemplateIndex].image
      : ex1);

  return (
    <QueryClientProvider client={queryClient}>
      <div className="flex flex-col bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] relative">
        {!showSuccess && (
          <div
            className="absolute inset-0 bg-[url('/src/assets/images/blur-bg.png')] bg-no-repeat bg-center bg-cover"
            style={{
              backgroundSize: '100% auto',
            }}
          />
        )}
        <div className="relative z-10 flex-1 overflow-y-auto">
          {!showSuccess ? (
            <>
              <CreateGuest
                activeStep={activeStep}
                completedSteps={completedSteps}
                onStepChange={handleStepChange}
                onClose={onClose}
              />
              <div>
                {activeStep === 1 && (
                  <CreateIV
                    onNextStep={handleNextStep}
                    onTemplateSelect={handleTemplateSelect}
                  />
                )}
                {activeStep === 2 && (
                  <AddGuest
                    onNextStep={handleNextStep}
                    selectedTemplateImage={selectedTemplateImage}
                    selectedTemplate={selectedTemplate}
                    onGuestsChange={handleGuestsChange}
                  />
                )}
                {activeStep === 3 && (
                  <Preview
                    onSuccess={handleSuccess}
                    selectedTemplate={selectedTemplate}
                    guests={guests}
                    onGuestsChange={handleGuestsChange}
                  />
                )}
              </div>
            </>
          ) : (
            <CreatedSuccess onClose={closeModals} />
          )}
        </div>
      </div>
    </QueryClientProvider>
  );
};

export default CreateGuestList;
