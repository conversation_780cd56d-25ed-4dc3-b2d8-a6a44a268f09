import { EventParkAPI } from '../event-park-api';

export interface TemplateCategory {
  id: string;
  name: string;
}

export interface GetTemplatesParams {
  category_id?: string;
  from?: string;
  to?: string;
  page?: number;
  per_page?: number;
  status?: 'active' | 'disabled';
}

export interface ParsedGuest {
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
}

export interface ParseGuestListResponse {
  guests: ParsedGuest[];
  parse_errors?: string[];
}

export interface ParseGuestListParams {
  eventId: string;
  file: File;
}

export const GuestList = {
  getTemplates: async (params?: GetTemplatesParams) => {
    return await EventParkAPI().get('/v1/templates', { params });
  },
  getTemplatesCategories: async () => {
    return await EventParkAPI().get('/v1/templates/categories');
  },
  parseGuestListFile: async ({
    eventId,
    file,
  }: ParseGuestListParams): Promise<ParseGuestListResponse> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await EventParkAPI().post(
      '/v1/user/events/guestlist/spreadsheet/parse',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        params: {
          event_id: eventId,
        },
      }
    );

    return response.data;
  },
};
