import { Navigate, Outlet, useNavigate } from 'react-router-dom';
import { ReactNode, useEffect, useState } from 'react';
import { isTokenValid } from './helpers';
import { useUserAuthStore } from './store/auth';
import { useEventStore } from './store/event';

export const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const { clearAuthData } = useUserAuthStore();
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (!token || (token && !isTokenValid(token))) {
      if (token) {
        clearAuthData();
        console.error('Session expired, please login again');
      }
      navigate('/login');
    }
  }, [token, navigate, clearAuthData]);

  if (!token || (token && !isTokenValid(token))) {
    return null;
  }

  return children;
};

export const AuthRoute = () => {
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();

  return token ? <Navigate to="/" replace /> : <Outlet />;
};

export const OnboardingRoute = ({ children }: { children: ReactNode }) => {
  const { clearAuthData } = useUserAuthStore();
  const { userEvents } = useEventStore();
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();

  useEffect(() => {
    const checkUserStatus = async () => {
      if (!token || !isTokenValid(token)) {
        clearAuthData();
        navigate('/login');
        return;
      }

      // Check if user has events from the store
      console.log('OnboardingRoute - userEvents from store:', userEvents);
      
      if (userEvents.length > 0) {
        console.log('OnboardingRoute - User has events, redirecting to dashboard');
        navigate('/', { replace: true });
        return;
      }
      
      setIsLoading(false);
    };

    checkUserStatus();
  }, [token, navigate, clearAuthData, userEvents]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }
  
  if (!token || !isTokenValid(token)) {
    return null;
  }

  return children;
};
