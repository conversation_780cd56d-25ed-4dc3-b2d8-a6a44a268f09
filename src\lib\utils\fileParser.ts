/* eslint-disable @typescript-eslint/no-explicit-any */
import <PERSON> from 'papaparse';
import * as XLSX from 'xlsx';

export interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export interface ParseResult {
  guests: Guest[];
  errors: string[];
  totalRows: number;
  validRows: number;
}

const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^[+]?[(]?[\d]{1,4}[)]?[-\s.]?[\d]{1,3}[-\s.]?[\d]{1,9}$/;
  return phoneRegex.test(phone.trim());
};

const normalizeColumnName = (columnName: string): string => {
  return columnName.toLowerCase().replace(/[^a-z]/g, '');
};

const mapRowToGuest = (row: any, index: number): { guest: Guest | null; errors: string[] } => {
  const errors: string[] = [];
    const normalizedRow: any = {};
  Object.keys(row).forEach(key => {
    const normalizedKey = normalizeColumnName(key);
    normalizedRow[normalizedKey] = row[key];
  });

  const firstName = (
    row['First Name'] || 
    row['FirstName'] || 
    row['first_name'] || 
    normalizedRow['firstname'] ||
    normalizedRow['fname'] ||
    ''
  ).toString().trim();

  const lastName = (
    row['Last Name'] || 
    row['LastName'] || 
    row['last_name'] || 
    normalizedRow['lastname'] ||
    normalizedRow['lname'] ||
    ''
  ).toString().trim();

  const email = (
    row['Email Address'] || 
    row['Email'] || 
    row['email'] || 
    normalizedRow['emailaddress'] ||
    normalizedRow['email'] ||
    ''
  ).toString().trim().toLowerCase();

  const phone = (
    row['Phone Number'] || 
    row['Phone'] || 
    row['phone'] || 
    row['Mobile'] ||
    normalizedRow['phonenumber'] ||
    normalizedRow['phone'] ||
    normalizedRow['mobile'] ||
    ''
  ).toString().trim();

  if (!firstName) {
    errors.push(`Row ${index + 2}: First name is required`);
  }
  
  if (!lastName) {
    errors.push(`Row ${index + 2}: Last name is required`);
  }
  
  if (!email) {
    errors.push(`Row ${index + 2}: Email is required`);
  } else if (!isValidEmail(email)) {
    errors.push(`Row ${index + 2}: Invalid email format (${email})`);
  }
  
  if (!phone) {
    errors.push(`Row ${index + 2}: Phone number is required`);
  } else if (!isValidPhone(phone)) {
    errors.push(`Row ${index + 2}: Invalid phone number format (${phone})`);
  }

  if (errors.length > 0) {
    return { guest: null, errors };
  }

  const guest: Guest = {
    id: Date.now() + index,
    firstName,
    lastName,
    email,
    phone,
  };

  return { guest, errors: [] };
};

export const parseCSVFile = (file: File): Promise<ParseResult> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        try {
          const guests: Guest[] = [];
          const errors: string[] = [];
          let validRows = 0;

          results.data.forEach((row: any, index: number) => {
            const { guest, errors: rowErrors } = mapRowToGuest(row, index);
            
            if (guest) {
              guests.push(guest);
              validRows++;
            }
            
            errors.push(...rowErrors);
          });

          resolve({
            guests,
            errors,
            totalRows: results.data.length,
            validRows,
          });
        } catch (error) {
          reject(new Error('Failed to parse CSV file: ' + (error as Error).message));
        }
      },
      error: (error) => {
        reject(new Error(`CSV parsing error: ${error.message}`));
      }
    });
  });
};

export const parseExcelFile = (file: File): Promise<ParseResult> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        if (jsonData.length < 2) {
          reject(new Error('Excel file must contain at least a header row and one data row'));
          return;
        }

        const headers = jsonData[0] as string[];
        const rows = jsonData.slice(1) as any[][];

        const guests: Guest[] = [];
        const errors: string[] = [];
        let validRows = 0;

        rows.forEach((row, index) => {
          const rowData: any = {};
          headers.forEach((header, i) => {
            rowData[header] = row[i] || '';
          });

          const { guest, errors: rowErrors } = mapRowToGuest(rowData, index);
          
          if (guest) {
            guests.push(guest);
            validRows++;
          }
          
          errors.push(...rowErrors);
        });

        resolve({
          guests,
          errors,
          totalRows: rows.length,
          validRows,
        });
      } catch (error) {
        reject(new Error('Failed to parse Excel file: ' + (error as Error).message));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsArrayBuffer(file);
  });
};

export const parseGuestListFile = async (file: File): Promise<ParseResult> => {
  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
  
  if (file.size > MAX_FILE_SIZE) {
    throw new Error('File size exceeds the maximum limit of 10MB. Please upload a smaller file.');
  }
  
  const fileName = file.name.toLowerCase();
  
  if (fileName.endsWith('.csv') || file.type === 'text/csv') {
    return parseCSVFile(file);
  } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
    return parseExcelFile(file);
  } else {
    throw new Error('Unsupported file type. Please upload a CSV or Excel file.');
  }
};

export const EXPECTED_COLUMNS = {
  'First Name': 'Required - Guest\'s first name',
  'Last Name': 'Required - Guest\'s last name', 
  'Email Address': 'Required - Valid email address',
  'Phone Number': 'Required - Phone number with country code'
};

export const COLUMN_VARIATIONS = {
  firstName: ['First Name', 'FirstName', 'first_name', 'fname'],
  lastName: ['Last Name', 'LastName', 'last_name', 'lname'],
  email: ['Email Address', 'Email', 'email'],
  phone: ['Phone Number', 'Phone', 'phone', 'Mobile']
};



