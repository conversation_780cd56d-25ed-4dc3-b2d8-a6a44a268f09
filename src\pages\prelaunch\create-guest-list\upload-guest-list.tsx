import { useState, useRef } from 'react';
import { TickCircle, Notepad2 } from 'iconsax-react';
import { Icon } from '../../../components/icons/icon';
import scan from '../../../assets/animations/scanniing.gif';
import { toast } from 'react-toastify';
import { GuestList, ParsedGuest } from '../../../lib/services/guest-list';
import { useEventStore } from '../../../lib/store/event';

// Local Guest interface for UI compatibility
interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface UploadGuestListProps {
  onNextStep?: () => void;
  onFormActiveChange?: (isActive: boolean) => void;
  onGuestsChange?: (guests: Guest[]) => void;
}

export const UploadGuestList = ({
  onNextStep,
  onGuestsChange,
}: UploadGuestListProps) => {
  const { selectedEvent } = useEventStore();
  const [fileName, setFileName] = useState<string>('');
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [isComplete, setIsComplete] = useState<boolean>(false);
  const [parsedGuests, setParsedGuests] = useState<Guest[]>([]);
  const [recordCount, setRecordCount] = useState<number>(0);
  const [errorCount, setErrorCount] = useState<number>(0);
  const [detailedErrors, setDetailedErrors] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const isValidFileType = (file: File): boolean => {
    const validTypes = [
      '.csv',
      '.xlsx',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
    ];
    return validTypes.some(
      (type) => file.name.toLowerCase().endsWith(type) || file.type === type
    );
  };

  // Transform backend ParsedGuest to UI Guest format
  const transformParsedGuestsToUIFormat = (
    parsedGuests: ParsedGuest[]
  ): Guest[] => {
    return parsedGuests.map((guest, index) => ({
      id: Date.now() + index,
      firstName: guest.first_name,
      lastName: guest.last_name,
      email: guest.email,
      phone: guest.phone_number,
    }));
  };

  // Helper function to get error count from parse_errors (handles both array and object formats)
  const getErrorCount = (
    parseErrors: Record<string, string> | string[] | undefined
  ): number => {
    if (!parseErrors) return 0;

    if (Array.isArray(parseErrors)) {
      return parseErrors.length;
    }

    // If it's an object, count the number of keys
    return Object.keys(parseErrors).length;
  };

  // Helper function to convert parse_errors to array format for consistent handling
  const normalizeParseErrors = (
    parseErrors: Record<string, string> | string[] | undefined
  ): string[] => {
    if (!parseErrors) return [];

    if (Array.isArray(parseErrors)) {
      return parseErrors;
    }

    // If it's an object, convert to array of error messages
    return Object.values(parseErrors);
  };

  const processFile = async (selectedFile: File) => {
    setIsScanning(true);
    setIsComplete(false);
    setErrorCount(0);

    try {
      // Check if event is selected
      if (!selectedEvent?.id) {
        throw new Error('No event selected. Please select an event first.');
      }

      // Call backend parsing service
      const result = await GuestList.parseGuestListFile({
        eventId: selectedEvent.id,
        file: selectedFile,
      });

      // Get error count and normalize errors
      const errorCount = getErrorCount(result.parse_errors);
      const normalizedErrors = normalizeParseErrors(result.parse_errors);

      // Store detailed errors for debugging (console log)
      if (errorCount > 0) {
        console.log('📋 File parsing errors:', normalizedErrors);
        setDetailedErrors(normalizedErrors);
      } else {
        // Clear detailed errors if no errors found
        setDetailedErrors([]);
      }

      if (result.guests.length === 0) {
        // No guests found - set error count and complete state for status display
        setErrorCount(errorCount);
        setIsComplete(true); // Set to true so status display shows
        return;
      }

      // Transform backend response to UI format
      const uiGuests = transformParsedGuestsToUIFormat(result.guests);

      setParsedGuests(uiGuests);
      setRecordCount(result.guests.length);
      setErrorCount(errorCount);
      setIsComplete(true);

      // Pass the parsed guests to the parent component
      if (onGuestsChange) {
        onGuestsChange(uiGuests);
      }

      // Concise success toast message
      const successMessage = `Successfully parsed ${result.guests.length} guest records`;
      toast.success(successMessage);
    } catch (error) {
      // Log error for debugging but don't show in UI - user will see scanning stopped
      console.error('File processing error:', error);
      setIsComplete(false);
      setParsedGuests([]);
      setRecordCount(0);
      setErrorCount(0);
      setDetailedErrors([]);
    } finally {
      setIsScanning(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      if (!isValidFileType(selectedFile)) {
        // Show error via toast instead of inline error
        toast.error('Invalid file type. Please upload a CSV or XLSX file.');
        setErrorCount(0);
        setDetailedErrors([]);
        return;
      }

      setFileName(selectedFile.name);
      processFile(selectedFile);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files?.[0];
    if (droppedFile) {
      if (!isValidFileType(droppedFile)) {
        setFileError('Invalid file type. Please upload a CSV or XLSX file.');
        setErrorCount(0);
        setDetailedErrors([]);
        return;
      }
      setFileName(droppedFile.name);
      processFile(droppedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleNext = () => {
    if (isComplete && parsedGuests.length > 0 && onNextStep) {
      onNextStep();
    }
  };

  const handleDownloadTemplate = () => {
    const templateUrl = '/template.xlsx';
    const link = document.createElement('a');
    link.href = templateUrl;
    link.download = 'template.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="flex-1 pt-8 px-2 md:px-0">
      <h3 className="md:text-[28px] text-lg font-medium">Upload Guest list</h3>
      <p className="md:text-base text-sm text-grey-250 mb-5">
        Got a documented list? upload right away!
      </p>

      <div className="mb-4 text-xs relative">
        <div className="bg-primary-150  py-1.5 pl-3 pr-12 rounded-2xl w-fit">
          <span className="text-primary-500 font-medium">
            Hey!, we helped you with a template
          </span>
        </div>
        <button
          onClick={handleDownloadTemplate}
          className="md:absolute -right-1 top-[3px] text-primary flex items-center gap-1 rounded-full py-0.5 px-2 bg-white border border-primary-950 italic font-bold">
          Download Template
          <Icon name="helpCircle" />
        </button>
      </div>
      <div
        className={` ${
          isComplete ? 'bg-primary-150' : 'bg-grey-350'
        } flex flex-col md:flex-row rounded-2xl  mb-2 cursor-pointer`}
        onClick={handleUploadClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}>
        <div
          className={` ${
            isComplete ? 'md:bg-primary-250' : 'md:bg-grey-850'
          } px-2 h-full mx-auto py-4 rounded-l-2xl`}>
          <Notepad2
            size={90}
            color={isComplete ? '#B8BBFA' : '#B3B3B3'}
            variant="Bulk"
            className="opacity-70"
          />
        </div>
        <div className="flex flex-col justify-between  w-full text-center md:text-start">
          <div className="md:ml-4 my-6">
            <h3
              className={`italic text-base font-medium ${
                isComplete ? 'text-primary-750' : 'text-black'
              }`}>
              {isComplete ? <> {fileName}</> : ' No file Uploaded yet '}
            </h3>
            <p
              className={`text-xs ${
                isComplete ? 'text-primary-500' : 'text-grey-550 '
              }`}>
              {isComplete
                ? 'Click to upload or change document'
                : 'Click to upload document'}
            </p>
          </div>
          <p
            className={`text-[10px] italic  py-[7px] rounded-br-2xl  ${
              isComplete
                ? 'text-primary-500 bg-primary-250'
                : 'text-dark-200 md:bg-grey-850'
            }`}>
            <span className="font-extrabold">Note:</span> Acceptable docs
            include (.CSV, .XLSX)
          </p>
        </div>
      </div>

      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
      />

      {isScanning && (
        <div className="flex items-center gap-2 border border-grey-150 w-fit py-1 px-3 rounded-full">
          <img src={scan} alt="scanning" className="h-4 w-4" />
          <p className="text-grey-500 text-sm font-medium">
            Parsing Document...
          </p>
        </div>
      )}

      {isComplete && (
        <div className="flex items-center gap-2 italic font-semibold text-sm">
          <p className="text-grey-550 py-1 px-3 rounded-full bg-grey-850">
            {recordCount} Record{recordCount !== 1 ? 's' : ''} Found
          </p>
          <div
            className={`flex items-center gap-2 px-3 py-1 rounded-full ${
              errorCount === 0
                ? 'text-green-600 bg-green-50'
                : 'text-orange-600 bg-orange-50'
            }`}>
            <TickCircle
              size={16}
              color={errorCount === 0 ? '#22C55E' : '#EA580C'}
              variant="Bold"
            />
            <span className="text-sm font-medium">
              {errorCount === 0
                ? 'No errors found'
                : `${errorCount} error${errorCount !== 1 ? 's' : ''} found`}
            </span>
          </div>
        </div>
      )}

      <div className="mt-38 py-3.5 border-t border-grey-850 flex justify-end">
        <button
          onClick={handleNext}
          disabled={!isComplete || recordCount === 0}
          className={`bg-primary text-white font-semibold py-3 px-6 rounded-full ${
            !isComplete || recordCount === 0
              ? 'opacity-50 cursor-not-allowed'
              : 'cursor-pointer hover:bg-primary/90'
          }`}>
          Next
        </button>
      </div>
    </div>
  );
};
